"""
Speaker Verification评估主脚本
使用iic/speech_campplus_sv_zh-cn_16k-common模型
在CN-Celeb2数据集上进行准确率验证
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Tuple
import torch
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from tqdm import tqdm

from sv_data_loader import CNCeleb2DataLoader
from sv_metrics import SVMetrics
import sv_config


class SVEvaluator:
    """Speaker Verification评估器"""
    
    def __init__(self):
        self.device = sv_config.DEVICE if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # 初始化模型
        print("Loading Speaker Verification model...")
        try:
            self.sv_pipeline = pipeline(
                task=Tasks.speaker_verification,
                model=sv_config.SV_MODEL_ID,
                device=self.device
            )
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Trying alternative loading method...")
            # 备用加载方法
            self.sv_pipeline = pipeline(
                task='speaker-verification',
                model=sv_config.SV_MODEL_ID,
                device=self.device
            )
            print("Model loaded with alternative method!")
        
        # 初始化数据加载器和指标计算器
        self.data_loader = CNCeleb2DataLoader(
            data_path=sv_config.CNCELEB2_DATA_PATH,
            sample_rate=sv_config.SAMPLE_RATE
        )
        self.metrics = SVMetrics()
        
        # 创建输出目录
        os.makedirs(sv_config.SV_OUTPUT_DIR, exist_ok=True)
    
    def extract_embedding(self, audio: np.ndarray) -> np.ndarray:
        """提取说话人嵌入"""
        try:
            # 使用pipeline提取嵌入
            result = self.sv_pipeline(audio)
            
            # 根据不同的返回格式处理
            if isinstance(result, dict):
                if 'embedding' in result:
                    embedding = result['embedding']
                elif 'output' in result:
                    embedding = result['output']
                else:
                    # 如果返回的是字典但没有明确的embedding字段，尝试获取第一个值
                    embedding = list(result.values())[0]
            else:
                embedding = result
            
            # 确保返回numpy数组
            if isinstance(embedding, torch.Tensor):
                embedding = embedding.cpu().numpy()
            elif isinstance(embedding, list):
                embedding = np.array(embedding)
            
            # 确保是1D数组
            if len(embedding.shape) > 1:
                embedding = embedding.flatten()
            
            return embedding
            
        except Exception as e:
            print(f"Error extracting embedding: {e}")
            # 返回零向量作为fallback
            return np.zeros(sv_config.EMBEDDING_DIM)
    
    def calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个嵌入之间的余弦相似度"""
        try:
            # 计算余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            print(f"Error calculating similarity: {e}")
            return 0.0
    
    def extract_all_embeddings(self, eval_data: Dict) -> Tuple[Dict, Dict]:
        """提取所有音频的嵌入"""
        print("Extracting speaker embeddings...")
        
        enroll_embeddings = {}
        test_embeddings = {}
        
        # 提取注册音频嵌入
        print("Extracting enrollment embeddings...")
        for audio_id, audio_info in tqdm(eval_data['enroll_audios'].items(), 
                                       desc="Enroll embeddings"):
            try:
                embedding = self.extract_embedding(audio_info['audio'])
                enroll_embeddings[audio_id] = embedding
            except Exception as e:
                print(f"Error processing enrollment audio {audio_id}: {e}")
                enroll_embeddings[audio_id] = np.zeros(sv_config.EMBEDDING_DIM)
        
        # 提取测试音频嵌入
        print("Extracting test embeddings...")
        for audio_id, audio_info in tqdm(eval_data['test_audios'].items(), 
                                       desc="Test embeddings"):
            try:
                embedding = self.extract_embedding(audio_info['audio'])
                test_embeddings[audio_id] = embedding
            except Exception as e:
                print(f"Error processing test audio {audio_id}: {e}")
                test_embeddings[audio_id] = np.zeros(sv_config.EMBEDDING_DIM)
        
        return enroll_embeddings, test_embeddings
    
    def evaluate_trials(self, eval_data: Dict) -> Dict:
        """评估所有试验"""
        print("Starting speaker verification evaluation...")
        
        # 提取所有嵌入
        enroll_embeddings, test_embeddings = self.extract_all_embeddings(eval_data)
        
        # 计算相似度分数
        print("Computing similarity scores...")
        scores = []
        labels = []
        detailed_results = []
        
        for trial in tqdm(eval_data['trials'], desc="Computing scores"):
            enroll_id = trial['enroll_id']
            test_id = trial['test_id']
            label = trial['label']
            
            if enroll_id in enroll_embeddings and test_id in test_embeddings:
                # 计算相似度
                similarity = self.calculate_similarity(
                    enroll_embeddings[enroll_id],
                    test_embeddings[test_id]
                )
                
                scores.append(similarity)
                labels.append(label)
                
                detailed_results.append({
                    'enroll_id': enroll_id,
                    'test_id': test_id,
                    'label': label,
                    'score': similarity,
                    'trial_type': 'target' if label == 1 else 'nontarget'
                })
            else:
                print(f"Warning: Missing embedding for trial {enroll_id} -> {test_id}")
        
        if not scores:
            raise ValueError("No valid scores computed!")
        
        # 计算评估指标
        print("Calculating evaluation metrics...")
        metrics = self.metrics.calculate_all_metrics(scores, labels)
        
        return {
            'metrics': metrics,
            'detailed_results': detailed_results,
            'embeddings': {
                'enroll_embeddings': enroll_embeddings,
                'test_embeddings': test_embeddings
            },
            'evaluation_info': {
                'model_id': sv_config.SV_MODEL_ID,
                'dataset': 'CN-Celeb2',
                'total_trials': len(eval_data['trials']),
                'valid_trials': len(scores),
                'evaluation_time': datetime.now().isoformat(),
                'device': self.device,
                'config': {
                    'sample_rate': sv_config.SAMPLE_RATE,
                    'max_duration': sv_config.MAX_DURATION,
                    'min_duration': sv_config.MIN_DURATION,
                    'normalize_audio': sv_config.NORMALIZE_AUDIO
                }
            }
        }
    
    def save_results(self, results: Dict):
        """保存评估结果"""
        # 保存总体结果（不包含嵌入）
        results_file = os.path.join(sv_config.SV_OUTPUT_DIR, sv_config.SV_RESULTS_FILE)
        save_data = {
            'metrics': results['metrics'],
            'evaluation_info': results['evaluation_info']
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        # 保存详细结果
        detailed_file = os.path.join(sv_config.SV_OUTPUT_DIR, sv_config.SV_DETAILED_RESULTS_FILE)
        df = pd.DataFrame(results['detailed_results'])
        df.to_csv(detailed_file, index=False, encoding='utf-8')
        
        # 保存嵌入（可选）
        embeddings_file = os.path.join(sv_config.SV_OUTPUT_DIR, sv_config.SV_EMBEDDINGS_FILE)
        np.savez_compressed(
            embeddings_file,
            enroll_embeddings=results['embeddings']['enroll_embeddings'],
            test_embeddings=results['embeddings']['test_embeddings']
        )
        
        print(f"Results saved to {results_file}")
        print(f"Detailed results saved to {detailed_file}")
        print(f"Embeddings saved to {embeddings_file}")
    
    def print_summary(self, results: Dict):
        """打印评估摘要"""
        self.metrics.print_metrics_summary(results['metrics'])
        
        eval_info = results['evaluation_info']
        print(f"\nEvaluation completed at: {eval_info['evaluation_time']}")
        print(f"Model: {eval_info['model_id']}")
        print(f"Dataset: {eval_info['dataset']}")
        print(f"Device: {eval_info['device']}")


def main():
    """主函数"""
    try:
        # 检查数据集路径配置
        if not sv_config.CNCELEB2_DATA_PATH:
            print("Error: CNCELEB2_DATA_PATH is not configured in sv_config.py")
            print("Please set the path to your CN-Celeb2 dataset.")
            return
        
        # 检查数据集文件
        trials_path = os.path.join(sv_config.CNCELEB2_DATA_PATH, sv_config.EVAL_TRIALS_PATH)
        enroll_path = os.path.join(sv_config.CNCELEB2_DATA_PATH, sv_config.EVAL_ENROLL_PATH)
        test_path = os.path.join(sv_config.CNCELEB2_DATA_PATH, sv_config.EVAL_TEST_PATH)
        
        missing_files = []
        for file_path, name in [(trials_path, "trials"), (enroll_path, "enroll"), (test_path, "test")]:
            if not os.path.exists(file_path):
                missing_files.append(f"{name}: {file_path}")
        
        if missing_files:
            print("Error: Missing required files:")
            for missing in missing_files:
                print(f"  - {missing}")
            print("\nPlease ensure CN-Celeb2 dataset is properly downloaded and configured.")
            print("You can also run prepare_cnceleb2.py to download and prepare the dataset.")
            return
        
        # 创建评估器
        evaluator = SVEvaluator()
        
        # 加载评估数据
        eval_data = evaluator.data_loader.get_evaluation_data(
            trials_path=sv_config.EVAL_TRIALS_PATH,
            enroll_path=sv_config.EVAL_ENROLL_PATH,
            test_path=sv_config.EVAL_TEST_PATH,
            max_trials=sv_config.MAX_TRIALS
        )
        
        # 运行评估
        results = evaluator.evaluate_trials(eval_data)
        
        # 保存和显示结果
        evaluator.save_results(results)
        evaluator.print_summary(results)
        
    except Exception as e:
        print(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
