# ASR模型评估工具

使用 `iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch` 模型在AISHELL-1数据集上进行准确率验证。

## 功能特性

### ASR评估功能
- 支持AISHELL-1数据集格式
- 计算字符错误率(CER)、词错误率(WER)和准确率
- 支持GPU和CPU推理
- 详细的评估报告和结果保存
- 中文文本预处理和标点符号处理

### Speaker Verification评估功能 🆕
- 支持CN-Celeb2数据集格式
- 使用 `iic/speech_campplus_sv_zh-cn_16k-common` 模型
- 计算EER (Equal Error Rate)和MinDCF (Minimum Detection Cost Function)
- 支持GPU和CPU推理
- 独立运行和配置，不影响现有ASR功能

## 安装依赖

```bash
pip install -r requirements.txt
```

## ASR评估 - AISHELL-1数据集

### 数据集准备

1. 下载AISHELL-1数据集
2. 确保数据集结构如下：
```
data/aishell/
├── test/
│   ├── wav.scp      # 音频文件路径列表
│   └── text         # 转录文本
└── wav/             # 音频文件目录
```

3. 修改 `config.py` 中的 `AISHELL_DATA_PATH` 为您的数据集路径

### 配置说明

在 `config.py` 中可以配置：

- `MODEL_ID`: ASR模型ID
- `DEVICE`: 使用的设备 ("cuda" 或 "cpu")
- `AISHELL_DATA_PATH`: AISHELL-1数据集路径
- `MAX_SAMPLES`: 最大测试样本数（None表示使用全部）
- `BATCH_SIZE`: 批处理大小
- `OUTPUT_DIR`: 结果输出目录

### 运行ASR评估

```bash
python asr_evaluation.py
```

### ASR输出结果

评估完成后会生成：

1. `results/evaluation_results.json`: 总体评估指标
2. `results/detailed_results.csv`: 每个样本的详细结果

### ASR评估指标

- **CER (Character Error Rate)**: 字符错误率
- **WER (Word Error Rate)**: 词错误率
- **Accuracy**: 准确率 (1 - CER)

## Speaker Verification评估 - CN-Celeb2数据集

### 数据集准备

1. 自动下载和准备CN-Celeb2数据集：
```bash
# 正常下载（支持断点续传和完整性检查）
python prepare_cnceleb2.py

# 强制重新下载（如果之前下载中断或文件损坏）
python prepare_cnceleb2.py --force
```

2. 或手动下载CN-Celeb2数据集：
   - 访问 https://www.openslr.org/82/
   - 下载 cn-celeb2_v2.tar.gzac (最新版本)
   - 解压到数据目录

3. 修改 `sv_config.py` 中的 `CNCELEB2_DATA_PATH` 为您的数据集路径

### Speaker Verification配置说明

在 `sv_config.py` 中可以配置：

- `SV_MODEL_ID`: Speaker Verification模型ID
- `DEVICE`: 使用的设备 ("cuda" 或 "cpu")
- `CNCELEB2_DATA_PATH`: CN-Celeb2数据集路径
- `MAX_TRIALS`: 最大试验数量（None表示使用全部）
- `BATCH_SIZE`: 批处理大小
- `SV_OUTPUT_DIR`: 结果输出目录

### 运行Speaker Verification评估

```bash
# 首先测试环境配置
python test_sv.py

# 运行评估
python sv_evaluation.py
```

### Speaker Verification输出结果

评估完成后会生成：

1. `sv_results/sv_evaluation_results.json`: 总体评估指标
2. `sv_results/sv_detailed_results.csv`: 每个试验的详细结果
3. `sv_results/speaker_embeddings.npz`: 说话人嵌入向量

### Speaker Verification评估指标

- **EER (Equal Error Rate)**: 等错误率
- **MinDCF (Minimum Detection Cost Function)**: 最小检测代价函数
- **Accuracy**: 在不同阈值下的准确率
- **Confusion Matrix**: 混淆矩阵

## 文件说明

### ASR评估相关文件
- `asr_evaluation.py`: ASR主评估脚本
- `data_loader.py`: AISHELL-1数据集加载器
- `metrics.py`: ASR评估指标计算
- `config.py`: ASR配置文件
- `prepare_dataset.py`: AISHELL-1数据集准备脚本

### Speaker Verification评估相关文件
- `sv_evaluation.py`: Speaker Verification主评估脚本
- `sv_data_loader.py`: CN-Celeb2数据集加载器
- `sv_metrics.py`: Speaker Verification评估指标计算
- `sv_config.py`: Speaker Verification配置文件
- `prepare_cnceleb2.py`: CN-Celeb2数据集准备脚本
- `test_sv.py`: Speaker Verification环境测试脚本

### 通用文件
- `requirements.txt`: 依赖包列表
- `test_setup.py`: ASR环境测试脚本

## 注意事项

### 通用注意事项
1. 首次运行会自动下载模型，需要网络连接
2. 建议使用GPU以提高推理速度
3. 确保有足够的内存和存储空间
4. 音频文件格式应为16kHz采样率的WAV文件

### Speaker Verification特别注意事项
1. CN-Celeb2数据集较大（约23GB gzac文件），下载需要时间和存储空间
2. Speaker Verification模型和ASR模型是独立的，可以分别配置不同的设备
3. 评估过程会提取所有音频的嵌入向量，需要较多内存
4. 首次运行建议先使用`test_sv.py`测试环境配置

## 故障排除

### ASR评估常见问题

1. **模型下载失败**: 检查网络连接，或手动下载模型
2. **CUDA内存不足**: 减小 `BATCH_SIZE` 或使用CPU
3. **数据集路径错误**: 检查 `config.py` 中的路径配置
4. **音频加载失败**: 确保音频文件格式正确且可访问

### Speaker Verification常见问题

1. **模型加载失败**:
   - 检查网络连接
   - 尝试运行 `test_sv_setup.py` 诊断问题
   - 确认modelscope版本兼容性

2. **数据集下载失败**:
   - 检查网络连接和存储空间（需要约23GB）
   - 如果下载中断，使用 `python prepare_cnceleb2.py --force` 重新下载
   - 可以手动下载cn-celeb2_v2.tar.gzac文件
   - 使用国内镜像源加速下载

3. **内存不足**:
   - 减小 `MAX_TRIALS` 限制试验数量
   - 减小 `BATCH_SIZE`
   - 使用CPU而非GPU

4. **嵌入提取失败**:
   - 检查音频文件格式和采样率
   - 确认模型加载正确
   - 查看详细错误日志

### 性能优化

- 使用GPU可显著提高推理速度
- 适当调整批处理大小以平衡速度和内存使用
- 对于大数据集，可以设置 `MAX_SAMPLES` 进行采样测试

## 示例输出

```
ASR EVALUATION SUMMARY
============================================================
Model: iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch
Dataset: AISHELL-1
Total samples: 7176
Valid samples: 7176
Device: cuda
Evaluation time: 2024-01-01T12:00:00
------------------------------------------------------------
Average Character Error Rate (CER): 0.0456
Average Word Error Rate (WER): 0.0456
Average Accuracy: 0.9544
============================================================
```
