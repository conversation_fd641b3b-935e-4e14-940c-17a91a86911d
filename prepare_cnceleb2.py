"""
CN-Celeb2数据集准备脚本
下载和准备CN-Celeb2数据集用于Speaker Verification评估
"""

import os
import tarfile
import requests
from tqdm import tqdm
from typing import List, Dict
import sv_config


# CN-Celeb2下载链接 - 只保留最新的gzac文件
CNCELEB2_URL = 'https://www.openslr.org/resources/82/cn-celeb2_v2.tar.gzac'
# 期望的文件大小（字节）- 用于完整性检查
CNCELEB2_EXPECTED_SIZE = 24696832000  # 约23GB


def download_file(url: str, filename: str, chunk_size: int = 8192, expected_size: int = None):
    """下载文件，支持断点续传和完整性检查"""
    headers = {}
    initial_pos = 0

    # 检查是否已有部分文件
    if os.path.exists(filename):
        file_size = os.path.getsize(filename)

        # 如果知道期望的文件大小，检查是否完整
        if expected_size and file_size == expected_size:
            print(f"File {filename} already exists and is complete ({file_size} bytes)")
            return
        elif file_size > 0:
            print(f"Found partial file {filename} ({file_size} bytes), resuming download...")
            initial_pos = file_size
            headers['Range'] = f'bytes={initial_pos}-'
        else:
            print(f"Found empty file {filename}, restarting download...")
            os.remove(filename)

    try:
        response = requests.get(url, headers=headers, stream=True)

        # 如果服务器不支持断点续传，重新下载
        if response.status_code == 416:  # Range Not Satisfiable
            print("Server doesn't support resume, restarting download...")
            initial_pos = 0
            if os.path.exists(filename):
                os.remove(filename)
            response = requests.get(url, stream=True)

        # 获取总文件大小
        content_length = response.headers.get('content-length')
        if content_length:
            total_size = int(content_length) + initial_pos
        else:
            total_size = initial_pos

        mode = 'ab' if initial_pos > 0 else 'wb'

        with open(filename, mode) as f:
            with tqdm(
                total=total_size if total_size > 0 else None,
                initial=initial_pos,
                unit='B',
                unit_scale=True,
                desc=os.path.basename(filename)
            ) as pbar:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))

        # 验证下载完成
        final_size = os.path.getsize(filename)
        if expected_size and final_size != expected_size:
            print(f"Warning: Downloaded file size ({final_size}) doesn't match expected size ({expected_size})")
        else:
            print(f"Downloaded: {filename} ({final_size} bytes)")

    except Exception as e:
        print(f"Error downloading {filename}: {e}")
        # 如果下载失败且文件存在但不完整，删除它
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            if not expected_size or file_size < expected_size:
                print(f"Removing incomplete file: {filename}")
                os.remove(filename)
        raise


def verify_file_integrity(file_path: str, expected_size: int = None) -> bool:
    """验证文件完整性"""
    if not os.path.exists(file_path):
        return False

    file_size = os.path.getsize(file_path)

    if expected_size:
        if file_size == expected_size:
            print(f"✓ File integrity verified: {file_path} ({file_size} bytes)")
            return True
        else:
            print(f"✗ File size mismatch: {file_path}")
            print(f"  Expected: {expected_size} bytes")
            print(f"  Actual: {file_size} bytes")
            return False
    else:
        # 如果没有期望大小，至少检查文件不为空
        if file_size > 0:
            print(f"✓ File exists and not empty: {file_path} ({file_size} bytes)")
            return True
        else:
            print(f"✗ File is empty: {file_path}")
            return False


def is_valid_tar_file(file_path: str) -> bool:
    """检查是否是有效的tar文件"""
    try:
        import tarfile
        with tarfile.open(file_path, 'r') as tar:
            # 尝试读取文件列表，如果成功说明文件格式正确
            members = tar.getnames()
            if len(members) > 0:
                print(f"✓ Valid tar file: {file_path} ({len(members)} entries)")
                return True
            else:
                print(f"✗ Empty tar file: {file_path}")
                return False
    except Exception as e:
        print(f"✗ Invalid tar file: {file_path} - {e}")
        return False


def extract_tar_gz(tar_path: str, extract_to: str):
    """解压tar.gz文件"""
    print(f"Extracting {tar_path}...")

    try:
        with tarfile.open(tar_path, 'r:gz') as tar:
            # 获取所有成员
            members = tar.getmembers()

            # 使用tqdm显示进度
            with tqdm(total=len(members), desc="Extracting") as pbar:
                for member in members:
                    tar.extract(member, extract_to)
                    pbar.update(1)

        print(f"Extraction completed: {tar_path}")

    except Exception as e:
        print(f"Error extracting {tar_path}: {e}")
        raise


def create_evaluation_files(data_dir: str):
    """创建评估所需的文件列表"""
    print("Creating evaluation file lists...")

    # 查找CN-Celeb2目录
    cnceleb2_dir = None
    for root, dirs, files in os.walk(data_dir):
        if 'CN-Celeb2' in dirs:
            cnceleb2_dir = os.path.join(root, 'CN-Celeb2')
            break
        elif os.path.basename(root) == 'CN-Celeb2':
            cnceleb2_dir = root
            break

    if not cnceleb2_dir:
        print("Warning: CN-Celeb2 directory not found. Please check the extraction.")
        return False

    print(f"Found CN-Celeb2 directory: {cnceleb2_dir}")

    # 创建eval目录
    eval_dir = os.path.join(data_dir, 'eval')
    os.makedirs(eval_dir, exist_ok=True)

    # 查找音频文件
    audio_files = []
    for root, dirs, files in os.walk(cnceleb2_dir):
        for file in files:
            if file.endswith(('.wav', '.flac', '.mp3')):
                rel_path = os.path.relpath(os.path.join(root, file), data_dir)
                speaker_id = os.path.basename(os.path.dirname(rel_path))
                audio_id = f"{speaker_id}_{os.path.splitext(file)[0]}"
                audio_files.append((audio_id, rel_path, speaker_id))

    if not audio_files:
        print("Warning: No audio files found in CN-Celeb2 directory.")
        return False

    print(f"Found {len(audio_files)} audio files")

    # 按说话人分组
    speakers = {}
    for audio_id, audio_path, speaker_id in audio_files:
        if speaker_id not in speakers:
            speakers[speaker_id] = []
        speakers[speaker_id].append((audio_id, audio_path))

    print(f"Found {len(speakers)} speakers")

    # 创建注册和测试列表（简单分割：每个说话人的前一半用于注册，后一半用于测试）
    enroll_list = []
    test_list = []

    for speaker_id, audios in speakers.items():
        mid_point = len(audios) // 2
        if mid_point == 0:
            mid_point = 1

        # 前一半用于注册
        for audio_id, audio_path in audios[:mid_point]:
            enroll_list.append(f"{audio_id} {audio_path}")

        # 后一半用于测试
        for audio_id, audio_path in audios[mid_point:]:
            test_list.append(f"{audio_id} {audio_path}")

    # 写入enroll.lst
    enroll_file = os.path.join(eval_dir, 'enroll.lst')
    with open(enroll_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(enroll_list))
    print(f"Created {enroll_file} with {len(enroll_list)} entries")

    # 写入test.lst
    test_file = os.path.join(eval_dir, 'test.lst')
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(test_list))
    print(f"Created {test_file} with {len(test_list)} entries")

    # 创建trials.lst（生成一些同说话人和不同说话人的试验对）
    trials = []

    # 为每个说话人创建一些试验
    for speaker_id, audios in speakers.items():
        speaker_enroll = [aid for aid, _ in audios[:len(audios)//2]]
        speaker_test = [aid for aid, _ in audios[len(audios)//2:]]

        # 同说话人试验（正样本）
        for enroll_id in speaker_enroll[:2]:  # 限制数量
            for test_id in speaker_test[:2]:
                trials.append(f"{enroll_id} {test_id} 1")

        # 不同说话人试验（负样本）- 与其他说话人的测试音频配对
        other_speakers = [s for s in speakers.keys() if s != speaker_id]
        for other_speaker in other_speakers[:3]:  # 限制数量
            other_test = [aid for aid, _ in speakers[other_speaker][len(speakers[other_speaker])//2:]]
            for enroll_id in speaker_enroll[:1]:
                for test_id in other_test[:1]:
                    trials.append(f"{enroll_id} {test_id} 0")

    # 写入trials.lst
    trials_file = os.path.join(eval_dir, 'trials.lst')
    with open(trials_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(trials))
    print(f"Created {trials_file} with {len(trials)} trials")

    return True


def check_dataset_structure(data_dir: str):
    """检查数据集结构"""
    print("\nChecking dataset structure...")

    expected_files = [
        'eval/trials.lst',
        'eval/enroll.lst',
        'eval/test.lst'
    ]

    missing_files = []
    for file_path in expected_files:
        full_path = os.path.join(data_dir, file_path)
        if os.path.exists(full_path):
            print(f"✓ Found: {file_path}")
        else:
            print(f"✗ Missing: {file_path}")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n⚠️  Missing {len(missing_files)} expected files.")
        return False

    return True


def prepare_cnceleb2_dataset(force_download: bool = False):
    """准备CN-Celeb2数据集"""
    # 设置路径
    if not sv_config.CNCELEB2_DATA_PATH:
        data_dir = "./data/cnceleb2"
        print(f"CNCELEB2_DATA_PATH not set, using default: {data_dir}")
    else:
        data_dir = sv_config.CNCELEB2_DATA_PATH

    download_dir = os.path.join(data_dir, "downloads")

    # 创建目录
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs(download_dir, exist_ok=True)

    print(f"Data directory: {data_dir}")
    print(f"Download directory: {download_dir}")

    if force_download:
        print("Force download mode enabled - will re-download even if file exists")

    # 下载CN-Celeb2数据集文件
    print("Downloading CN-Celeb2 dataset...")

    filename = "cn-celeb2_v2.tar.gzac"
    file_path = os.path.join(download_dir, filename)

    # 检查文件是否存在且完整
    need_download = True
    if force_download:
        if os.path.exists(file_path):
            print(f"Removing existing file for forced re-download: {filename}")
            os.remove(file_path)
    elif os.path.exists(file_path):
        print(f"Found existing file: {filename}")
        print("Verifying file integrity...")

        # 检查文件大小
        if verify_file_integrity(file_path, CNCELEB2_EXPECTED_SIZE):
            # 检查是否是有效的tar文件
            if is_valid_tar_file(file_path):
                print("File verification passed, skipping download.")
                need_download = False
            else:
                None
                # print("File appears corrupted, will re-download.")
                # os.remove(file_path)
        else:
            print("File size incorrect, will re-download.")
            os.remove(file_path)

    if need_download:
        print(f"Downloading {filename}...")
        download_file(CNCELEB2_URL, file_path, expected_size=CNCELEB2_EXPECTED_SIZE)

        # 下载完成后再次验证
        print("Verifying downloaded file...")
        if not verify_file_integrity(file_path, CNCELEB2_EXPECTED_SIZE):
            raise Exception("Downloaded file failed integrity check")

        if not is_valid_tar_file(file_path):
            raise Exception("Downloaded file is not a valid tar archive")

        print("Download and verification completed successfully!")

    # 使用验证过的文件进行解压
    combined_file = file_path

    # 解压文件
    print("Extracting dataset...")
    extract_tar_gz(combined_file, data_dir)

    # 创建评估文件
    if not create_evaluation_files(data_dir):
        print("Warning: Failed to create evaluation files.")
        return False

    # 检查数据集结构
    return check_dataset_structure(data_dir)


def main():
    """主函数"""
    import sys

    print("CN-Celeb2 Dataset Preparation")
    print("="*50)

    # 检查命令行参数
    force_download = False
    if len(sys.argv) > 1:
        if '--force' in sys.argv or '-f' in sys.argv:
            force_download = True
            print("Force download mode enabled")
        elif '--help' in sys.argv or '-h' in sys.argv:
            print("Usage: python prepare_cnceleb2.py [options]")
            print("Options:")
            print("  --force, -f    Force re-download even if file exists")
            print("  --help, -h     Show this help message")
            return

    try:
        success = prepare_cnceleb2_dataset(force_download=force_download)

        if success:
            print("\n✅ CN-Celeb2 dataset preparation completed successfully!")
            print("You can now run: python sv_evaluation.py")

            # 更新配置提示
            if not sv_config.CNCELEB2_DATA_PATH:
                print("\n📝 Don't forget to update sv_config.py:")
                print("   Set CNCELEB2_DATA_PATH = './data/cnceleb2'")
        else:
            print("\n❌ Dataset preparation completed with warnings.")
            print("Please check the error messages above.")

    except Exception as e:
        print(f"\n❌ Dataset preparation failed: {e}")
        import traceback
        traceback.print_exc()
        print("\nTip: If download was interrupted, try running with --force to re-download")


if __name__ == "__main__":
    main()
